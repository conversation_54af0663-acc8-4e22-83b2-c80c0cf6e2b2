'use client';

import Spline from '@splinetool/react-spline';
import React, { useState, useCallback, useEffect } from 'react';
import SplineErrorBoundary from './SplineErrorBoundary';
import { useInView } from 'react-intersection-observer';

const SplineWebComponent = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [shouldRender, setShouldRender] = useState(false);

  const { ref, inView } = useInView({
    triggerOnce: true,   // Only trigger once
    threshold: 0.3,       // 30% visible to trigger
  });

  const onLoad = useCallback(() => {
    setIsLoading(false);
    setHasError(false);
  }, []);

  const onError = useCallback((error: any) => {
    console.warn('Spline scene failed to load:', error);
    setIsLoading(false);
    setHasError(true);
  }, []);

  // Start rendering when in view
  useEffect(() => {
    if (inView) {
      setShouldRender(true);
    }
  }, [inView]);

  return (
    <SplineErrorBoundary>
      <div
        ref={ref}
        className="drop-shadow-[0_0_40px_rgba(124,58,237,0.12)]"
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          width: '100%',
          padding: '0 2rem',
          boxSizing: 'border-box',
          overflow: 'visible',
          position: 'relative',
        }}
      >
        {/* Loading Text */}
        {isLoading && (
          <div
            style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              color: '#a855f7',
              fontSize: '18px',
              fontWeight: 'bold',
              zIndex: 10,
              textAlign: 'center',
            }}
          >
            Loading 3D Model...
          </div>
        )}

        {/* Error Message */}
        {hasError && (
          <div
            style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              color: '#ef4444',
              fontSize: '16px',
              textAlign: 'center',
              zIndex: 10,
            }}
          >
            <div>3D Model Unavailable</div>
            <div style={{ fontSize: '14px', marginTop: '8px', color: '#9ca3af' }}>
              Please check your connection
            </div>
          </div>
        )}

        {/* Spline Model */}
        {shouldRender && !hasError && (
          <div
            style={{
              transform: 'scale(1.3)',
              transformOrigin: 'center',
              overflow: 'visible',
            }}
          >
            <Spline
              scene="/models/robots.splinecode"
              style={{
                width: '100%',
                minWidth: '800px',
                maxWidth: '1500px',
                height: '700px',
                background: 'transparent',
                display: 'block',
                overflow: 'visible',
              }}
              onLoad={onLoad}
              onError={onError}
              renderOnDemand={false}
            />
          </div>
        )}
      </div>
    </SplineErrorBoundary>
  );
};

export default SplineWebComponent;
